# Proposal Export Endpoint

This document describes the new POST endpoint for exporting proposal volumes to PDF or DOCX format, which reverses the functionality of the `move_to_review` function.

## Overview

The new endpoint `/proposals/export-volumes` receives volume data for different sections, converts it to the format `all_volumes: List[List[Dict[str, Any]] | None]`, and then processes it through the export pipeline.

## Files Created/Modified

### 1. New Router: `AIService/routers/proposal_export.py`
- **Endpoint**: `POST /proposals/export-volumes`
- **Purpose**: Receives volume data and exports to PDF/DOCX format
- **Authentication**: Requires valid user authentication
- **Validation**: Ensures users can only export for their own tenant

### 2. Enhanced Service: `AIService/services/proposal/rfp/generate_rfp.py`
Added three new methods to the `RFPGenerationService` class:

#### `_convert_volumes_to_pdf_bytes()`
- Converts proposal volumes to PDF bytes using `RfpDraftExportController`
- Saves PDF data to datametastore
- Processes each volume individually

#### `_convert_volumes_to_docx_bytes()`
- Converts proposal volumes to DOCX format using `RfpDraftExportController`
- Includes table of contents generation
- Processes each volume individually

#### `_move_all_volumes_to_format()`
- Moves all volumes to the format queue for further processing
- Encrypts volume data before storage
- Handles version management

### 3. Updated Main Application: `AIService/main.py`
- Registered the new `proposal_export_router`
- Added necessary imports

### 4. Test Script: `AIService/test_proposal_export.py`
- Provides testing functionality for the new methods
- Includes API usage examples
- Demonstrates proper request format

## API Usage

### Request Format
```json
{
  "tenant_id": "your_tenant_id",
  "opportunity_id": "your_opportunity_id", 
  "source": "custom",
  "client_short_name": "YourClient",
  "cover_page": 4380,
  "export_type": 2,
  "job_submitted_by": "<EMAIL>",
  "all_volumes": [
    [
      {
        "section_title": "Executive Summary",
        "content": "Content here...",
        "page_limit": 2
      }
    ],
    null
  ],
  "all_table_of_contents": [
    [
      {
        "title": "Executive Summary", 
        "page_limit": 2
      }
    ],
    []
  ]
}
```

### Response Format
```json
{
  "message": "Successfully exported 2 volumes to PDF format",
  "opportunity_id": "your_opportunity_id",
  "tenant_id": "your_tenant_id",
  "export_type": "PDF", 
  "volumes_processed": 2
}
```

## Export Types
- **1**: DOCX format
- **2**: PDF format (default if unknown type provided)

## Process Flow

1. **Receive Request**: Endpoint receives volume data and export parameters
2. **Validate Access**: Ensures user can only export for their tenant
3. **Convert Format**: Based on `export_type`, calls appropriate conversion method:
   - PDF: `_convert_volumes_to_pdf_bytes()`
   - DOCX: `_convert_volumes_to_docx_bytes()`
4. **Move to Format Queue**: Calls `_move_all_volumes_to_format()` to queue for final processing
5. **Return Response**: Provides success confirmation with processing details

## Key Features

- **Reverses move_to_review**: Converts stored volume data back to exportable formats
- **Format Support**: Supports both PDF and DOCX export
- **Volume Processing**: Handles multiple volumes with proper numbering
- **Error Handling**: Comprehensive error handling and logging
- **Authentication**: Secure access with tenant validation
- **Encryption**: Uses existing encryption services for data security

## Dependencies

The implementation leverages existing services:
- `RfpDraftExportController` for PDF/DOCX generation
- `ProposalDecodingService` for encryption
- `ProposalsFormatQueueController` for queue management
- Authentication and database services

## Testing

Run the test script to verify functionality:
```bash
python AIService/test_proposal_export.py
```

This will test all three new methods and provide API usage examples.

## Error Handling

The endpoint includes comprehensive error handling for:
- Authentication failures
- Invalid tenant access
- Missing or malformed data
- Export processing errors
- Database connection issues

All errors are logged with appropriate detail levels for debugging and monitoring.
