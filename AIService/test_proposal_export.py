#!/usr/bin/env python3
"""
Test script for the proposal export endpoint
"""
import asyncio
import json
import sys
from typing import Dict, Any, List

# Add the current directory to Python path
sys.path.append('.')

from services.proposal.rfp.generate_rfp import RFPGenerationService
from loguru import logger

async def test_export_methods():
    """Test the new export methods"""
    
    # Initialize the service
    rfp_service = RFPGenerationService()
    
    # Sample test data
    test_volumes = [
        [
            {
                "section_title": "Executive Summary",
                "content": "This is a test executive summary for volume 1.",
                "page_limit": 2
            },
            {
                "section_title": "Technical Approach", 
                "content": "This is a test technical approach for volume 1.",
                "page_limit": 5
            }
        ],
        [
            {
                "section_title": "Management Approach",
                "content": "This is a test management approach for volume 2.",
                "page_limit": 3
            }
        ],
        None  # Empty volume 3
    ]
    
    test_toc = [
        [
            {"title": "Executive Summary", "page_limit": 2},
            {"title": "Technical Approach", "page_limit": 5}
        ],
        [
            {"title": "Management Approach", "page_limit": 3}
        ],
        []
    ]
    
    # Test parameters
    tenant_id = "test_tenant_123"
    opportunity_id = "test_opp_456"
    source = "custom"
    client_short_name = "TestClient"
    cover_page = 4380
    job_submitted_by = "<EMAIL>"
    
    logger.info("Testing proposal export methods...")
    
    try:
        # Test PDF conversion
        logger.info("Testing PDF conversion...")
        await rfp_service._convert_volumes_to_pdf_bytes(
            proposal_volumes=test_volumes,
            all_table_of_contents=test_toc,
            tenant_id=tenant_id,
            opps_id=opportunity_id,
            source=source,
            client_short_name=client_short_name,
            cover_page=cover_page,
            job_submitted_by=job_submitted_by
        )
        logger.info("✅ PDF conversion method executed successfully")
        
        # Test DOCX conversion
        logger.info("Testing DOCX conversion...")
        await rfp_service._convert_volumes_to_docx_bytes(
            proposal_volumes=test_volumes,
            all_table_of_contents=test_toc,
            tenant_id=tenant_id,
            opps_id=opportunity_id,
            source=source,
            client_short_name=client_short_name,
            cover_page=cover_page,
            job_submitted_by=job_submitted_by
        )
        logger.info("✅ DOCX conversion method executed successfully")
        
        # Test move to format
        logger.info("Testing move to format...")
        await rfp_service._move_all_volumes_to_format(
            proposal_volumes=test_volumes,
            tenant_id=tenant_id,
            opps_id=opportunity_id,
            source=source,
            client_short_name=client_short_name,
            cover_page=cover_page,
            export_type=2,  # PDF
            job_submitted_by=job_submitted_by
        )
        logger.info("✅ Move to format method executed successfully")
        
        logger.info("🎉 All export methods tested successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {e}")
        raise

def print_api_usage():
    """Print example API usage"""
    
    example_request = {
        "tenant_id": "your_tenant_id",
        "opportunity_id": "your_opportunity_id",
        "source": "custom",  # or "sam", "ebuy"
        "client_short_name": "YourClient",
        "cover_page": 4380,
        "export_type": 2,  # 1=DOCX, 2=PDF
        "job_submitted_by": "<EMAIL>",
        "all_volumes": [
            [
                {
                    "section_title": "Executive Summary",
                    "content": "Your executive summary content here...",
                    "page_limit": 2
                },
                {
                    "section_title": "Technical Approach",
                    "content": "Your technical approach content here...",
                    "page_limit": 5
                }
            ],
            [
                {
                    "section_title": "Management Approach", 
                    "content": "Your management approach content here...",
                    "page_limit": 3
                }
            ],
            None  # Empty volume (will be skipped)
        ],
        "all_table_of_contents": [
            [
                {"title": "Executive Summary", "page_limit": 2},
                {"title": "Technical Approach", "page_limit": 5}
            ],
            [
                {"title": "Management Approach", "page_limit": 3}
            ],
            []
        ]
    }
    
    print("\n" + "="*60)
    print("API ENDPOINT USAGE EXAMPLE")
    print("="*60)
    print("\nEndpoint: POST /proposals/export-volumes")
    print("\nExample Request Body:")
    print(json.dumps(example_request, indent=2))
    print("\nExample Response:")
    print(json.dumps({
        "message": "Successfully exported 2 volumes to PDF format",
        "opportunity_id": "your_opportunity_id",
        "tenant_id": "your_tenant_id", 
        "export_type": "PDF",
        "volumes_processed": 2
    }, indent=2))
    print("\n" + "="*60)

if __name__ == "__main__":
    print("🚀 Starting proposal export test...")
    
    # Print API usage example
    print_api_usage()
    
    # Run the async test
    try:
        asyncio.run(test_export_methods())
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
