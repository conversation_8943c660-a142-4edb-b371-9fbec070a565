from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from database import get_customer_db
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.scheduler_service.proposal_scheduler_service import ProposalSchedulerService
from dependencies.auth import get_current_user
from schemas.auth_schemas import CurrentUser
from loguru import logger

router = APIRouter(prefix="/proposals", tags=["proposal-export"])

class VolumeExportRequest(BaseModel):
    """Request model for exporting proposal volumes from ProposalsInReview"""
    tenant_id: str = Field(..., description="Tenant ID")
    opportunity_id: str = Field(..., description="Opportunity ID")
    source: str = Field(..., description="Source type (sam, ebuy, custom)")
    client_short_name: str = Field(..., description="Client short name")
    cover_page: Optional[int] = Field(None, description="Cover page ID")
    export_type: int = Field(..., description="Export type (1=DOCX, 2=PDF)")
    job_submitted_by: str = Field(..., description="User who submitted the job")
    version: Optional[int] = Field(None, description="Version to export (defaults to latest)")

class VolumeExportResponse(BaseModel):
    """Response model for volume export"""
    message: str
    opportunity_id: str
    tenant_id: str
    export_type: str
    volumes_processed: int

@router.post(
    "/export-volumes",
    response_model=VolumeExportResponse,
    summary="Export proposal volumes to PDF or DOCX format"
)
async def export_proposal_volumes(
    request: VolumeExportRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Export proposal volumes to the specified format (PDF or DOCX).
    This endpoint retrieves data from ProposalsInReview and reconstructs the volumes.
    """
    try:
        # Validate user access
        if request.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: cannot export volumes for other tenants"
            )

        logger.info(f"Exporting proposal volumes for opportunity {request.opportunity_id} by user: {current_user.email}")

        # Initialize services
        rfp_service = RFPGenerationService()
        proposal_service = ProposalSchedulerService()

        # Retrieve proposal volumes from ProposalsInReview
        from services.proposal.proposal_volumes_retrival import ProposalVolumeRetrievalService
        retrieval_service = ProposalVolumeRetrievalService()

        logger.info("Retrieving proposal volumes from ProposalsInReview...")
        proposal_volumes = await retrieval_service.get_all_volumes_from_review(
            db=db,
            tenant_id=request.tenant_id,
            opportunity_id=request.opportunity_id
        )

        if not proposal_volumes or all(vol is None for vol in proposal_volumes):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No proposal volumes found for opportunity {request.opportunity_id}"
            )

        # Retrieve table of contents from opportunity data
        logger.info("Retrieving table of contents from opportunity data...")
        all_table_of_contents = await rfp_service.get_all_table_of_contents(
            opportunity_id=request.opportunity_id,
            tenant_id=request.tenant_id,
            source=request.source
        )

        logger.info(f"Converting all volumes to {request.export_type} format and saving to datametastore...")

        if request.export_type == 2:  # PDF
            await proposal_service._convert_volumes_to_pdf_bytes(
                proposal_volumes,
                all_table_of_contents,
                request.tenant_id,
                request.opportunity_id,
                request.source,
                request.client_short_name,
                request.cover_page,
                request.job_submitted_by
            )
            export_type_str = "PDF"
        elif request.export_type == 1:  # DOCX
            await proposal_service._convert_volumes_to_docx_bytes(
                proposal_volumes,
                all_table_of_contents,
                request.tenant_id,
                request.opportunity_id,
                request.source,
                request.client_short_name,
                request.cover_page,
                request.job_submitted_by
            )
            export_type_str = "DOCX"
        else:
            logger.warning(f"Unknown export type: {request.export_type}, defaulting to PDF")
            await proposal_service._convert_volumes_to_pdf_bytes(
                proposal_volumes,
                all_table_of_contents,
                request.tenant_id,
                request.opportunity_id,
                request.source,
                request.client_short_name,
                request.cover_page,
                request.job_submitted_by
            )
            export_type_str = "PDF"

        logger.info("Moving all volumes to format...")
        await rfp_service.move_all_volumes_to_format(
            proposal_volumes,
            request.tenant_id,
            request.opportunity_id,
            request.source,
            request.client_short_name,
            request.cover_page or 0,  # Default to 0 if None
            request.export_type,
            request.job_submitted_by
        )

        volumes_processed = sum(1 for volume in proposal_volumes if volume is not None)
        
        logger.info(f"Successfully exported {volumes_processed} volumes to {export_type_str} format")
        
        return VolumeExportResponse(
            message=f"Successfully exported {volumes_processed} volumes to {export_type_str} format",
            opportunity_id=request.opportunity_id,
            tenant_id=request.tenant_id,
            export_type=export_type_str,
            volumes_processed=volumes_processed
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting proposal volumes for opportunity {request.opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error while exporting proposal volumes: {str(e)}"
        )
